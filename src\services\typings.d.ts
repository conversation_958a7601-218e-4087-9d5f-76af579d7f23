declare namespace API {
  /**
   * 统一返回信息格式
   */
  type ResType<T> = {
    errCode: number;
    data?: T;
    msg?: string;
  };

  interface IResponseDate {
    createdAt: Date;
    updatedAt: Date;
  }

  type LoginResponse = {
    token: string;
    refreshToken: string;
    user: Omit<User, 'password'>;
  };

  type Dictionarie = {
    /** 字典ID，主键 */
    id: number;
    /** 字典类型 */
    type: string;
    /** 字典编码 */
    code: string;
    /** 字典名称 */
    name: string;
    /** 别名 */
    alias?: string;
    /** 字典描述（可选） */
    description?: string;
    /** 排序 */
    sortOrder: number;
    /** 状态 */
    status: number;
  };

  type User = {
    id: number;
    username: string;
    password: string;
    nickname?: string;
    avatar?: string;
    email?: string;
    isActive?: boolean;
    roles?: Role[];
    features?: Feature[];
  };

  /** 系统功能 */
  type Feature = {
    /** 功能编号，主键 */
    code: string;
    /** 功能名称 */
    name: string;
    /** 父级功能编号（可选） */
    parentCode?: string;
    /** 功能图标（可选） */
    icon?: string;
    /** 功能路径 */
    path: string;
    /** 排序 */
    orderIndex: number;
    /** 描述（可选） */
    description?: string;
    permissions?: Permission[];
  };

  /** 仅限点 */
  type Permission = {
    id: number;
    /** 权限名称 */
    name: string;
    /** 权限描述 */
    description?: string;
    /** 关联的功能编号 */
    featureCode: string;
    feature?: Feature;
    roles?: Role[];
  };

  /** 角色 */
  type Role = {
    id: number;
    /** 角色名称 */
    name: string;
    /** 角色描述 */
    description?: string;
    users?: User[];
    permissions?: Permission[];
  };

  /** 行政区划 */
  type Area = {
    /** 行政区划编码,主键 */
    code: string;
    /** 行政区划名称 */
    name: string;
    /** 父级行政区划编码 */
    parentCode?: string;
  };

  /** 车辆 */
  type Vehicle = {
    /** 车辆ID */
    id: number;
    /** 车牌号 */
    plateNumber: string;
    /** 实时纬度 */
    latitude?: number;
    /** 实时经度 */
    longitude?: number;
    /** 车辆状态（空闲/服务中） */
    status: string;
    /** 关联的员工信息 */
    employee?: Employee;
  };

  /** 员工 */
  type Employee = {
    /** 员工ID */
    id: number;
    /** 真实姓名 */
    name: string;
    /** 手机号 */
    phone: string;
    /** 头像 */
    avatar?: string;
    /** 接单等级（1-5级） */
    level?: number;
    /** 工作经验（月） */
    workExp?: number;
    /** 服务评分（0-5分） */
    rating?: number;
    /** 钱包余额 */
    walletBalance: number;
    /** 所属车辆ID */
    vehicleId?: number;
    /** 关联的车辆信息 */
    vehicle?: Vehicle;
    /** 关联的订单列表 */
    orders?: Order[];
    /** 状态：0-禁用 1-启用 */
    status: number;
  };

  /** 客户 */
  type Customer = {
    /** 客户ID */
    id: number;
    /** 手机号 */
    phone: string;
    /** 昵称 */
    nickname?: string;
    /** 头像 */
    avatar?: string;
    /** 性别：0-女 1-男 */
    gender?: number;
    /** 详细地址 */
    address?: string;
    /** 会员状态：0-非会员 1-会员 */
    memberStatus: number;
    /** 积分值 */
    points: number;
    /** 最后登录时间 */
    lastLoginTime?: Date;
    /** 关联的宠物列表 */
    pets?: Pet[];
    /** 关联的订单列表 */
    orders?: Order[];
    /** 关联的权益卡列表 */
    membershipCards?: CustomerMembershipCard[];
    /** 状态：0-禁用 1-启用 */
    status: number;
  };

  /** 轮播图 */
  type Banner = {
    /** 轮播图ID */
    id: number;
    /** 图片链接 */
    imageURL: string;
    /** 跳转链接 */
    jumpLink?: string;
    /** 展示优先级（1-10） */
    priority: number;
  };

  /** 宠物 */
  interface Pet {
    /** 宠物ID */
    id: number;
    /** 关联用户ID */
    customerId: number;
    /** 宠物名称 */
    name: string;
    /** 头像 */
    avatar?: string;
    /** 宠物类型，例如 猫、狗 */
    type: string;
    /** 品种，例如金毛 */
    breed?: string;
    /** 性别：0-未知 1-男 2-女 */
    gender: number;
    /** 出生年月 */
    birthday?: Date;
    /** 年龄（月），虚拟字段 */
    bri?: number;
    /** 毛发类型 */
    hairType: string;
    /** 体重Kg */
    weight?: number;
    /** 是否疫苗 */
    isVaccine?: boolean;
    /** 是否绝育 */
    isSterilization?: boolean;
    /** 是否驱虫 */
    isRepellent?: boolean;
    /** 排序值 */
    orderIndex: number;
    /** 关联客户信息 */
    customer?: Customer;
  }

  /** 服务品牌 */
  type ServiceType = {
    /** 类目ID */
    id: number;
    /** 服务名称 */
    name: string;
    /** 服务描述 */
    description?: string;
    /** 服务类型 */
    type: string;
    /** 排序值 */
    orderIndex: number;
    /** 关联的服务列表 */
    services?: Service[];
  };

  /** 服务项目 */
  type Service = {
    /** 服务ID */
    id: number;
    /** 服务品牌ID */
    serviceTypeId: number;
    /** 服务名称 */
    serviceName: string;
    /** 服务logo */
    logo?: string;
    /** 服务描述 */
    description?: string;
    /** 基础价格 */
    basePrice: number;
    /** 宠物类型 */
    petTypes: string;
    /** 适用体型 */
    size?: string | null;
    /** 宠物重量描述 */
    weightDescription?: string;
    /** 适用毛发类型 */
    hairType?: string | null;
    /** 是否按距离计费 */
    distanceChargeFlag: boolean;
    /** 是否支持权益卡抵扣 */
    cardDiscountFlag: boolean;
    /** 排序值 */
    orderIndex: number;
    /** 是否发布 */
    published: boolean;
    /** 关联的服务类型信息 */
    serviceType?: ServiceType;
    /** 增项服务 */
    additionalServices?: AdditionalService[];
  };

  /** 增项服务 */
  type AdditionalService = {
    /** 增项服务ID */
    id: number;
    /** 服务名称 */
    name: string;
    /** 服务图标 */
    icon?: string;
    /** 服务类型 */
    type: string;
    /** 服务价格 */
    price: number;
    /** 服务时长(分钟) */
    duration: number;
    /** 服务说明 */
    description?: string;
  };

  /** 订单明细 */
  interface OrderDetail {
    /** 明细ID */
    id: number;
    /** 关联订单ID */
    orderId: number;
    /** 关联服务ID */
    serviceId: number;
    /** 服务名称，确保删除服务后订单明细的服务名称不丢失 */
    serviceName: string;
    /** 服务基础价格，确保服务价格变更后订单明细的价格不受影响 */
    servicePrice: number;
    /** 关联宠物ID */
    petId?: number;
    /** 宠物名称，确保删除宠物后订单明细的宠物名称不丢失 */
    petName: string;
    /** 宠物类型，确保删除宠物后订单明细的宠物类型不丢失 */
    petType: string;
    /** 宠物品种，确保删除宠物后订单明细的宠物品种不丢失 */
    petBreed?: string;
    /** 下单时间 */
    orderTime: Date;
    /** 状态 */
    status: string;
    /** 关联订单信息 */
    order?: Order;
    /** 关联服务信息 */
    service?: Service;
    /** 关联宠物信息 */
    pet?: Pet;
    /** 关联增项服务列表 */
    additionalServices?: AdditionalService[];
  }

  /** 评价信息 */
  interface Review {
    /** 评价ID */
    id: number;
    /** 关联订单ID */
    orderId: number;
    /** 评分 */
    rating: number;
    /** 文字评价 */
    comment?: string;
    /** 照片链接列表 */
    photoURLs?: string[];
    /** 关联的订单信息 */
    order?: Order;
  }

  /** 投诉建议大类 */
  enum ComplaintCategory {
    投诉 = 'complaint',
    建议 = 'suggestion',
  }

  /** 投诉建议小类 */
  enum ComplaintSubCategory {
    订单投诉 = 'order',
    人员投诉 = 'employee',
    平台建议 = 'platform',
    服务建议 = 'service',
  }

  /** 投诉建议处理状态 */
  enum ComplaintStatus {
    待处理 = 'pending',
    处理中 = 'processing',
    已解决 = 'resolved',
    已关闭 = 'closed',
  }

  /** 投诉建议 */
  interface Complaint extends IResponseDate {
    /** 投诉建议ID */
    id: number;
    /** 关联用户ID */
    customerId: number;
    /** 关联订单ID（可选） */
    orderId?: number;
    /** 关联员工ID（可选） */
    employeeId?: number;
    /** 大类：投诉/建议 */
    category: ComplaintCategory;
    /** 小类：订单/人员/平台/服务 */
    subCategory: ComplaintSubCategory;
    /** 标题 */
    title: string;
    /** 内容 */
    content: string;
    /** 联系方式 */
    contactInfo?: string;
    /** 图片URL数组 */
    photoURLs?: string[];
    /** 处理状态 */
    status: ComplaintStatus;
    /** 处理结果 */
    result?: string;
    /** 处理人员ID */
    handlerId?: number;
    /** 处理时间 */
    handledAt?: Date;
    /** 关联的用户信息 */
    customer?: Customer;
    /** 关联的订单信息 */
    order?: Order;
    /** 关联的员工信息 */
    employee?: Employee;
    /** 处理人员信息 */
    handler?: User;
  }

  /** 投诉建议统计信息 */
  interface ComplaintStatistics {
    /** 总数 */
    total: number;
    /** 今日新增 */
    todayCount: number;
    /** 本周新增 */
    weekCount: number;
    /** 本月新增 */
    monthCount: number;
    /** 待处理数量 */
    pendingCount: number;
    /** 处理中数量 */
    processingCount: number;
    /** 已解决数量 */
    resolvedCount: number;
    /** 已关闭数量 */
    closedCount: number;
    /** 按大类统计 */
    categoryStats: {
      complaint: number;
      suggestion: number;
    };
    /** 按小类统计 */
    subCategoryStats: {
      order: number;
      employee: number;
      platform: number;
      service: number;
    };
    /** 平均处理时长（小时） */
    avgHandleTime: number;
  }

  // ==================== 投诉建议统计接口类型定义 ====================

  /** 投诉建议概览统计 */
  interface ComplaintOverviewStats {
    complaintStats: {
      total: number;
      today: number;
      week: number;
      month: number;
      processedCount: number;
      processRate: number;
    };
    statusStats: Array<{
      status: string;
      count: number;
      percentage: number;
    }>;
    categoryStats: Array<{
      category: string;
      count: number;
      percentage: number;
    }>;
    subCategoryStats: Array<{
      subCategory: string;
      count: number;
      percentage: number;
    }>;
  }

  /** 投诉建议趋势统计 */
  interface ComplaintTrendStats {
    period: string;
    totalCount: number;
    complaintCount: number;
    suggestionCount: number;
    resolvedCount: number;
    resolveRate: number;
  }

  /** 客户投诉建议统计 */
  interface ComplaintCustomerStats {
    customerId: number;
    customerName: string;
    customerPhone: string;
    customerAvatar?: string;
    memberStatus: number;
    totalCount: number;
    complaintCount: number;
    suggestionCount: number;
    resolvedCount: number;
    resolveRate: number;
  }

  /** 员工投诉统计 */
  interface ComplaintEmployeeStats {
    employeeId: number;
    employeeName: string;
    employeePhone: string;
    employeeAvatar?: string;
    employeeRating: number;
    complaintCount: number;
    resolvedCount: number;
    pendingCount: number;
    resolveRate: number;
  }

  /** 处理效率统计 */
  interface ComplaintProcessingEfficiencyStats {
    averageProcessingHours: number;
    fastProcessingCount: number;
    slowProcessingCount: number;
    fastProcessingRate: number;
    slowProcessingRate: number;
    categoryEfficiency: Array<{
      category: string;
      count: number;
      averageHours: number;
      fastCount: number;
      slowCount: number;
    }>;
    subCategoryEfficiency: Array<{
      subCategory: string;
      count: number;
      averageHours: number;
      fastCount: number;
      slowCount: number;
    }>;
  }

  /** 热点问题分析 */
  interface ComplaintHotIssuesStats {
    hotKeywords: Array<{
      keyword: string;
      count: number;
    }>;
    subCategoryHotIssues: Array<{
      subCategory: string;
      count: number;
    }>;
  }

  /** 状态分布统计 */
  interface ComplaintStatusDistributionStats {
    statusStats: Array<{
      status: string;
      count: number;
      percentage: number;
    }>;
    categoryStats: Array<{
      category: string;
      count: number;
      percentage: number;
    }>;
    subCategoryStats: Array<{
      subCategory: string;
      count: number;
      percentage: number;
    }>;
  }

  /** 投诉建议排行榜 */
  interface ComplaintRankingStats {
    type: string;
    list: Array<{
      customerId?: number;
      customerName?: string;
      employeeId?: number;
      employeeName?: string;
      totalCount: number;
      complaintCount: number;
      suggestionCount: number;
    }>;
  }

  /** 处理时效统计 */
  interface ComplaintProcessingTimeStats {
    averageProcessingHours: number;
    fastProcessingCount: number;
    slowProcessingCount: number;
    fastProcessingRate: number;
    slowProcessingRate: number;
    timeDistribution: {
      '0-24h': number;
      '24-72h': number;
      '72h+': number;
    };
  }

  /** 解决率统计 */
  interface ComplaintResolveRateStats {
    groupBy: string;
    data: Array<{
      name: string;
      count: number;
      percentage: number;
    }>;
    totalResolveRate: number;
  }

  /** 处理人员统计 */
  interface ComplaintHandlerStats {
    handlerId: number;
    handledCount: number;
    avgProcessingHours: number;
    resolvedCount: number;
    resolveRate: number;
  }

  /** 时间分布统计 */
  interface ComplaintTimeDistributionStats {
    period: number;
    label: string;
    count: number;
    complaintCount: number;
    suggestionCount: number;
  }

  /** 满意度统计 */
  interface ComplaintSatisfactionStats {
    total: number;
    satisfiedCount: number;
    unsatisfiedCount: number;
    neutralCount: number;
    satisfactionRate: number;
    unsatisfactionRate: number;
    neutralRate: number;
  }

  /** 分页响应 */
  interface PaginatedResponse<T> {
    list: T[];
    total: number;
    page: number;
    pageSize: number;
  }

  /** 订单 */
  interface Order extends IResponseDate {
    /** 订单ID */
    id: number;
    /** 订单编号 */
    sn: string;
    /** 客户ID */
    customerId: number;
    /** 员工ID */
    employeeId?: number;
    /** 订单状态 */
    status: string;
    /** 下单时间 */
    orderTime: Date;
    /** 预约服务时间 */
    serviceTime?: Date;
    /** 地址ID */
    addressId?: number;
    /** 服务地址 */
    address: string;
    /** 服务地址经度 */
    longitude: number;
    /** 服务地址纬度 */
    latitude: number;
    /** 服务地址详情 */
    addressDetail: string;
    /** 服务地址备注 */
    addressRemark?: string;
    /** 订单总费用 */
    totalFee: number;
    /** 权益卡抵扣金额 */
    cardDeduction: number;
    /** 代金券抵扣金额 */
    couponDeduction: number;
    /** 关联客户信息 */
    customer?: Customer;
    /** 关联员工信息 */
    employee?: Employee;
    /** 关联地址信息 */
    customerAddress?: CustomerAddress;
    /** 订单明细列表 */
    orderDetails?: OrderDetail[];
    /** 服务变更记录列表 */
    changeLogs?: ServiceChangeLog[];
    /** 评价信息 */
    review?: Review;
    /** 投诉记录列表 */
    complaints?: Complaint[];
  }

  /** 服务照片 */
  interface ServicePhoto extends IResponseDate {
    /** 服务照片ID */
    id: number;
    /** 关联订单ID */
    orderId: number;
    /** 员工ID */
    employeeId: number;
    /** 服务前照片 */
    beforePhotos: string[];
    /** 服务前拍照时间 */
    beforePhotoTime?: string;
    /** 服务后照片 */
    afterPhotos: string[];
    /** 服务后拍照时间 */
    afterPhotoTime?: string;
    /** 关联的订单信息 */
    order?: Order;
    /** 关联的员工信息 */
    employee?: Employee;
  }

  /** 服务日志 */
  interface ServiceChangeLog extends IResponseDate {
    /** 变更记录ID */
    id: number;
    /** 关联订单ID */
    orderId: number;
    /** 变更类型 */
    changeType: string;
    /** 用户端发起人 */
    customerId?: number;
    /** 员工端发起人 */
    employeeId?: number;
    /** 描述 */
    description?: string;
    /** 关联的订单信息 */
    order?: Order;
    /** 用户端发起人信息 */
    customer?: Customer;
    /** 员工端发起人信息 */
    employee?: Employee;
  }

  enum ApplicableScope {
    不限 = 'all',
    所有服务 = 'allServices',
    指定服务类别 = 'serviceType',
    指定服务品牌 = 'serviceCategory',
    指定服务 = 'service',
    所有商品 = 'allProducts',
    指定商品类别 = 'productCategory',
    指定商品 = 'product',
  }

  /** 权益卡类型 */
  interface MembershipCardType {
    /** 卡类型唯一标识 */
    id: number;
    /** 名称 */
    name: string;
    /** 售价 */
    price: number;
    /** 权益卡类型，折扣卡或次卡 */
    type: 'discount' | 'times';
    /** 有效期天数 */
    validDays?: number;
    /** 折扣率 */
    discountRate?: number;
    /** 可用次数 */
    usageLimit?: number;
    /** 适用范围 */
    applicableScope: ApplicableScope;
    /** 适用服务类型名称列表，适用范围为指定服务类型时必填 */
    applicableServiceTypes?: string[];
    /** 适用服务品牌ID列表，适用范围为指定服务品牌时必填 */
    applicableServiceCategories?: number[];
    /** 适用服务ID列表，适用范围为指定服务时必填 */
    applicableServices?: number[];
    /** 适用商品类别ID列表，适用范围为指定商品类别时必填 */
    applicableProductTypes?: number[];
    /** 适用商品ID列表，适用范围为指定商品时必填 */
    applicableProducts?: number[];
    /** 权益描述 */
    description?: string;
    /** 是否启用 */
    isEnabled: boolean;
    /** 关联的用户权益卡信息 */
    userMembershipCards?: CustomerMembershipCard[];
    /** 发放数量（虚拟字段） */
    userCount?: number;
  }

  /** 代金券 */
  interface Coupon {
    /** 代金券唯一标识 */
    id: number;
    /** 售价 */
    price: number;
    /** 面值 */
    amount: number;
    /** 使用门槛 */
    threshold: number;
    /** 有效天数 */
    validDays?: number;
    /** 可用次数 */
    usageLimit?: number;
    /** 适用范围 */
    applicableScope: ApplicableScope;
    /** 适用服务类型名称列表，适用范围为指定服务类型时必填 */
    applicableServiceTypes?: string[];
    /** 适用服务品牌ID列表，适用范围为指定服务品牌时必填 */
    applicableServiceCategories?: number[];
    /** 适用服务ID列表，适用范围为指定服务时必填 */
    applicableServices?: number[];
    /** 适用商品类别ID列表，适用范围为指定商品类别时必填 */
    applicableProductTypes?: number[];
    /** 适用商品ID列表，适用范围为指定商品时必填 */
    applicableProducts?: number[];
    /** 权益描述 */
    description?: string;
    /** 是否启用 */
    isEnabled: boolean;
    /** 关联的用户代金券信息 */
    userCoupons?: CustomerCoupon[];
  }

  /** 用户权益卡 */
  interface CustomerMembershipCard extends IResponseDate {
    /** 用户权益卡唯一标识 */
    id: number;
    /** 关联用户ID */
    customerId: number;
    /** 关联权益卡类型ID */
    cardTypeId: number;
    /** 购买时间 */
    purchaseTime: Date;
    /** 到期时间 */
    expiryTime?: Date;
    /** 剩余使用次数，-1表示不限次数 */
    remainTimes: number;
    /** 状态：active-有效，expired-已过期，used-已用完 */
    status: 'active' | 'expired' | 'used';
    /** 关联的用户信息 */
    customer?: Customer;
    /** 关联的权益卡类型信息 */
    cardType?: MembershipCardType;
  }

  /** 用户代金券 */
  interface CustomerCoupon extends IResponseDate {
    /** 客户代金券唯一标识 */
    id: number;
    /** 关联客户 */
    customerId: number;
    /** 关联代金券 */
    couponId: number;
    /** 领取时间 */
    receiveTime: Date;
    /** 到期时间，null表示无到期时间 */
    expiryTime?: Date;
    /** 剩余使用次数，-1表示不限次数 */
    remainTimes: number;
    /** 状态：active-有效，used-已使用，expired-已过期 */
    status: 'active' | 'used' | 'expired';
    /** 最后一次使用时间 */
    lastUseTime?: Date;
    /** 关联的客户信息 */
    customer?: Customer;
    /** 关联的代金券信息 */
    coupon?: Coupon;
    /** 使用记录 */
    usageRecords?: CouponUsageRecord[];
  }

  interface CouponUsageRecord {
    /** 使用记录唯一标识 */
    id: number;
    /** 关联的客户代金券 */
    customerCouponId: number;
    /** 使用时间 */
    useTime: Date;
    /** 使用的订单 */
    orderId: number;
    /** 关联的客户代金券信息 */
    customerCoupon?: CustomerCoupon;
    /** 关联的订单信息 */
    order?: Order;
  }

  /**
   * 权益卡订单状态枚举
   */
  export enum MembershipCardOrderStatus {
    待付款 = 'pending_payment',
    已付款 = 'paid',
    已取消 = 'cancelled',
    已退款 = 'refunded',
  }

  /**
   * 权益卡订单属性
   */
  interface MembershipCardOrder {
    /** 订单ID */
    id: number;
    /** 订单编号 */
    sn: string;
    /** 关联的用户ID */
    customerId: number;
    /** 关联的权益卡类型ID */
    cardTypeId: number;
    /** 订单金额 */
    amount: number;
    /** 订单状态 */
    status: MembershipCardOrderStatus;
    /** 创建时间 */
    createdAt?: Date;
    /** 支付时间 */
    payTime?: Date;
    /** 取消时间 */
    cancelTime?: Date;
    /** 退款时间 */
    refundTime?: Date;
    /** 微信支付预支付ID */
    prepayId?: string;
    /** 备注 */
    remark?: string;
    /** 关联的用户 */
    customer?: Customer;
    /** 关联的权益卡类型 */
    cardType?: MembershipCardType;
  }

  /** 推广记录表 */
  interface PromotionRecord {
    /** 记录ID */
    id: number;
    /** 分享人微信openid */
    sharerOpenid: string;
    /** 分享人系统用户ID */
    sharerUserId: number;
    /** 分享时间 */
    shareTime: Date;
    /** 注册人微信openid */
    registrantOpenid: string;
    /** 注册人系统用户ID */
    registrantUserId?: number;
    /** 注册时间 */
    registerTime?: Date;
    /** 分享码 */
    promotionCode: string;
    /** 状态：有效、已使用、过期 */
    status: string;
    sharer: Customer;
    registrant: Customer;
  }

  // ==================== 数据一致性维护相关类型定义 ====================

  /** 数据一致性检查结果 */
  interface DataConsistencyCheckResult {
    inconsistentCount: number;
    inconsistentRecords: Array<{
      id: number;
      serviceId: number;
      detail_service_name: string;
      actual_service_name: string;
      detail_service_price: number;
      actual_service_price: number;
    }>;
    isConsistent: boolean;
  }

  /** 冗余字段统计信息 */
  interface RedundantFieldStats {
    totalRecords: number;
    filledServiceNames: number;
    filledServicePrices: number;
    emptyServiceNames: number;
    emptyServicePrices: number;
    serviceNameFillRate: string;
    servicePriceFillRate: string;
  }

  /** 修复操作结果 */
  interface RepairResult {
    repairedCount: number;
    message?: string;
  }

  /** 同步服务结果 */
  interface SyncServiceResult {
    affectedCount: number;
  }

  /** 订单原价检查结果 */
  interface OrderPriceCheckResult {
    missingCount: number;
    missingOrders: Array<{
      id: number;
      sn: string;
      originalPrice: number;
      totalFee: number;
      cardDeduction: number;
      couponDeduction: number;
      orderDetails: any[];
    }>;
    needsRepair: boolean;
  }

  /** 订单原价统计信息 */
  interface OrderPriceStats {
    totalOrders: number;
    missingOriginalPrice: number;
    validOriginalPrice: number;
    abnormalPriceOrders: number;
    originalPriceFillRate: string;
    abnormalPriceRate: string;
  }

  /** 综合维护任务结果 */
  interface MaintenanceTaskResult {
    timestamp: string;
    consistencyCheck: DataConsistencyCheckResult;
    repairResult: RepairResult;
    repairMissingFields: number;
    statistics: RedundantFieldStats;
  }

  /** 验证结果 */
  interface ValidationResult {
    isValid: boolean;
    statistics: RedundantFieldStats;
    inconsistentCount: number;
    message: string;
  }

  // ==================== 异常价格检测相关类型定义 ====================

  /** 异常价格订单 */
  interface AbnormalPriceOrder {
    id: number;
    sn: string;
    originalPrice: number;
    totalFee: number;
    cardDeduction: number;
    couponDeduction: number;
    priceDifference: number;
    differenceRate: number;
    abnormalReason: string[];
    calculatedPrice: number;
    isConfirmedAbnormal: boolean;
  }

  /** 异常价格检查结果 */
  interface AbnormalPriceCheckResult {
    abnormalCount: number;
    threshold: number;
    abnormalOrders: AbnormalPriceOrder[];
  }

  /** 异常价格详情 */
  interface AbnormalPriceDetail {
    order: {
      id: number;
      sn: string;
      originalPrice: number;
      totalFee: number;
      cardDeduction: number;
      couponDeduction: number;
      status: string;
      orderTime: string;
    };
    priceAnalysis: {
      calculatedPrice: number;
      priceDifference: number;
      differenceRate: number;
      abnormalReason: string[];
      isConfirmedAbnormal: boolean;
    };
    orderDetails: Array<{
      id: number;
      serviceId: number;
      serviceName: string;
      servicePrice: number;
      currentServicePrice: number;
      priceDifference: number;
    }>;
  }

  /** 异常价格修正结果 */
  interface AbnormalPriceFixResult {
    fixedCount: number;
    message: string;
  }

  /** 异常价格阈值设置结果 */
  interface AbnormalPriceThresholdResult {
    threshold: number;
    message: string;
  }

  /** 异常价格统计报告 */
  interface AbnormalPriceReport {
    threshold: number;
    totalAbnormalCount: number;
    confirmedAbnormalCount: number;
    suspiciousCount: number;
    reasonStatistics: Array<{
      reason: string;
      count: number;
      percentage: string;
    }>;
    topAbnormalOrders: AbnormalPriceOrder[];
  }
}
