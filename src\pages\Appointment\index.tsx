import { OrderStatus } from '@/constant';
import {
  cancel,
  complete,
  deliver,
  index,
  refund,
  remove,
  start,
  transfer,
} from '@/services/order';
import { ActionType, ProColumns, ProTable } from '@ant-design/pro-components';
import { Button, Card, message, Popconfirm, Space, Tabs } from 'antd';
import React, { useRef, useState } from 'react';
import AcceptModal from './AcceptModal';
import DetailModal from './DetailModal';
import OrderLog from './OrderLog';
import RefundAuditModal from './RefundAuditModal';
import ReviewDetailModal from './ReviewDetailModal';
import { OverviewDashboard, StatusBoardView, TimelineView } from './components';
// import EditModal from './EditModal';

const Appointment: React.FC = () => {
  const actionRef = useRef<ActionType>();
  const [modalVisible, setModalVisible] = useState(false);

  const [showDetail, setShowDetail] = useState<boolean>(false);
  const [showLog, setShowLog] = useState<boolean>(false);
  const [current, setCurrent] = useState<API.Order | undefined>(undefined);

  // 添加审核弹窗状态
  const [refundAuditVisible, setRefundAuditVisible] = useState<boolean>(false);

  // 添加查看评价弹窗状态
  const [reviewDetailVisible, setReviewDetailVisible] =
    useState<boolean>(false);

  /** 派单 */
  const handleAccept = async (record: API.Order, employeeId: number) => {
    const { id, status } = record;
    let method;
    if (status === OrderStatus.待接单) {
      method = deliver;
    } else if (status === OrderStatus.待服务) {
      method = transfer;
    } else {
      message.error('订单状态错误');
      return;
    }
    const response = await method(id, employeeId);
    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('派单成功');
      actionRef?.current?.reload();
      setModalVisible(false);
    }
  };

  /** 开始服务 */
  const handleStart = async (record: API.Order, employeeId: number) => {
    const { id } = record;
    const response = await start(id, employeeId);
    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('修改成功');
      actionRef?.current?.reload();
    }
  };

  /** 完成服务 */
  const handleComplete = async (record: API.Order, employeeId: number) => {
    const { id } = record;
    const response = await complete(id, employeeId);
    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('修改成功');
      actionRef?.current?.reload();
    }
  };

  /** 退款 */
  const handleRefund = async (record: API.Order) => {
    const { sn } = record;
    const response = await refund(sn);
    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('退款成功');
      actionRef?.current?.reload();
    }
  };

  /** 退款审核 */
  const handleAuditRefund = async (record: API.Order) => {
    setCurrent(record);
    setRefundAuditVisible(true);
  };

  /** 查看评价 */
  const handleViewReview = (record: API.Order) => {
    setCurrent(record);
    setReviewDetailVisible(true);
  };

  /** 取消订单 */
  const handleCancel = async (record: API.Order) => {
    const { id } = record;
    const response = await cancel(id);
    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('取消成功');
      actionRef?.current?.reload();
    }
  };

  /** 删除订单 */
  const handleDel = async (record: API.Order) => {
    const { id } = record;
    const response = await remove(id);
    if (response.errCode) {
      message.error(response.msg);
    } else {
      message.success('删除成功');
      actionRef?.current?.reload();
    }
  };

  const columns: ProColumns<API.Order, 'text'>[] = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      hidden: true,
      hideInSearch: true,
    },
    {
      title: '订单号',
      dataIndex: 'sn',
      key: 'sn',
      width: 100,
      fixed: 'left',
      copyable: true,
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 100,
      render: (_, record) => {
        return record.orderDetails?.[0]?.service?.serviceName;
      },
      hideInSearch: true,
    },
    {
      title: '类型',
      dataIndex: 'name',
      key: 'name',
      width: 100,
      render: (_, record) => {
        return record.orderDetails?.[0]?.service?.serviceType?.name;
      },
      hideInSearch: true,
    },
    {
      title: '客户姓名',
      dataIndex: ['customer', 'nickname'],
      key: 'nickname',
      width: 100,
      hideInSearch: true,
    },
    {
      title: '手机号',
      dataIndex: ['customer', 'phone'],
      key: 'phone',
      width: 100,
      copyable: true,
    },
    {
      title: '预约时间',
      dataIndex: 'serviceTime',
      key: 'serviceTime',
      width: 100,
      valueType: 'dateTime',
      hideInSearch: true,
    },
    {
      title: '订单金额',
      dataIndex: 'totalFee',
      key: 'totalFee',
      width: 100,
      valueType: 'money',
      hideInSearch: true,
    },
    {
      title: '服务人员',
      dataIndex: ['employee', 'name'],
      key: 'employeename',
      width: 100,
    },
    {
      title: '订单状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
      valueEnum: {
        待付款: '待付款',
        待接单: '待接单',
        待服务: '待服务',
        已出发: '已出发',
        服务中: '服务中',
        已完成: '已完成',
        已评价: '已评价',
        已取消: '已取消',
        已退款: '已退款',
      },
      filters: true,
      hideInSearch: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 100,
      valueType: 'dateTime',
      hideInSearch: true,
    },
    {
      title: '操作',
      key: 'action',
      valueType: 'option',
      width: 220,
      align: 'center',
      render: (_, record) => (
        <Space>
          <a
            onClick={() => {
              setCurrent(record);
              setShowDetail(true);
            }}
          >
            查看详情
          </a>
          <a
            onClick={() => {
              setCurrent(record);
              setShowLog(true);
            }}
          >
            查看日志
          </a>
          {record.status === OrderStatus.已评价 && (
            <a
              onClick={() => {
                handleViewReview(record);
              }}
            >
              查看评价
            </a>
          )}
          {record.status === OrderStatus.待接单 && (
            <a
              onClick={() => {
                setCurrent(record);
                setModalVisible(true);
              }}
            >
              派单
            </a>
          )}
          {[OrderStatus.待服务, OrderStatus.已出发].includes(
            record.status as any,
          ) && (
            <>
              <Popconfirm
                title="确定要通过后台将服务状态改为【已开始】吗？"
                onConfirm={() => {
                  handleStart(record, record.employeeId!);
                }}
                okText="确定"
                cancelText="取消"
              >
                <a>开始服务</a>
              </Popconfirm>
              <a
                onClick={() => {
                  setCurrent(record);
                  setModalVisible(true);
                }}
              >
                改派订单
              </a>
            </>
          )}
          {record.status === OrderStatus.服务中 && (
            <Popconfirm
              title="确定要通过后台将服务状态改为【已完成】吗？"
              onConfirm={() => {
                handleComplete(record, record.employeeId!);
              }}
              okText="确定"
              cancelText="取消"
            >
              <a>完成订单</a>
            </Popconfirm>
          )}
          {[OrderStatus.退款中].includes(record.status as any) && (
            <a onClick={() => handleAuditRefund(record)}>退款审核</a>
          )}
          {[OrderStatus.待接单].includes(record.status as any) && (
            <Popconfirm
              title="确定退款吗？"
              onConfirm={() => {
                handleRefund(record);
              }}
              okText="确定"
              cancelText="取消"
            >
              <a style={{ color: '#ff4d4f' }}>退款</a>
            </Popconfirm>
          )}
          {record.status === OrderStatus.待付款 && (
            <Popconfirm
              title="确定取消吗？"
              onConfirm={() => {
                handleCancel(record);
              }}
              okText="确定"
              cancelText="取消"
            >
              <a style={{ color: '#ff4d4f' }}>取消订单</a>
            </Popconfirm>
          )}
          {record.status === OrderStatus.已取消 && (
            <Popconfirm
              title="确定删除吗？"
              onConfirm={() => {
                handleDel(record);
              }}
              okText="确定"
              cancelText="取消"
            >
              <a type="link" style={{ color: '#ff4d4f' }}>
                删除
              </a>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];

  const tabItems = [
    {
      key: 'overview',
      label: '概览仪表板',
      children: <OverviewDashboard />,
    },
    {
      key: 'board',
      label: '状态看板',
      children: (
        <StatusBoardView
          onViewDetail={(order) => {
            setCurrent(order);
            setShowDetail(true);
          }}
          onViewLog={(order) => {
            setCurrent(order);
            setShowLog(true);
          }}
          onAssign={(order) => {
            setCurrent(order);
            setModalVisible(true);
          }}
          onStart={handleStart}
          onComplete={handleComplete}
          onViewReview={handleViewReview}
          onAuditRefund={handleAuditRefund}
        />
      ),
    },
    {
      key: 'timeline',
      label: '时间安排',
      children: (
        <TimelineView
          onViewDetail={(order) => {
            setCurrent(order);
            setShowDetail(true);
          }}
          onAssign={(order) => {
            setCurrent(order);
            setModalVisible(true);
          }}
        />
      ),
    },
    {
      key: 'table',
      label: '详细列表',
      children: (
        <ProTable<API.Order>
          actionRef={actionRef}
          rowKey="id"
          columns={columns}
          request={async (params, _sort, filter) => {
            const { errCode, msg, data } = await index({ ...params, filter });
            if (errCode) {
              message.error(msg || '列表查询失败');
              return {
                data: [],
                total: 0,
              };
            }
            return {
              data: data?.list || [],
              total: data?.total || 0,
            };
          }}
          scroll={{ x: '100%' }}
          toolBarRender={() => [
            <Button
              key="add"
              type="primary"
              onClick={() => {
                setCurrent(undefined);
                setModalVisible(true);
              }}
            >
              新增
            </Button>,
          ]}
        />
      ),
    },
  ];

  return (
    <>
      <Card>
        <Tabs defaultActiveKey="overview" items={tabItems} />
      </Card>
      <AcceptModal
        open={modalVisible}
        current={current}
        onClose={() => {
          setModalVisible(false);
        }}
        onSave={async (employeeId: number) => {
          if (current) {
            await handleAccept(current, employeeId);
          }
        }}
      />
      <DetailModal
        open={showDetail}
        current={current}
        onClose={() => {
          setShowDetail(false);
        }}
      />
      <OrderLog
        open={showLog}
        order={current}
        onClose={() => {
          setShowLog(false);
        }}
      />
      <RefundAuditModal
        open={refundAuditVisible}
        current={current}
        onClose={() => {
          setRefundAuditVisible(false);
        }}
        onSuccess={() => {
          actionRef?.current?.reload();
        }}
      />
      <ReviewDetailModal
        visible={reviewDetailVisible}
        order={current || null}
        onClose={() => {
          setReviewDetailVisible(false);
        }}
      />
    </>
  );
};

export default Appointment;
