import {
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons';
import {
  Alert,
  Card,
  Col,
  Descriptions,
  Row,
  Table,
  Tag,
  Typography,
} from 'antd';
import React from 'react';

const { Title, Text } = Typography;

interface ResultDisplayProps {
  consistencyResult?: API.DataConsistencyCheckResult;
  orderPriceResult?: API.OrderPriceCheckResult;
  repairResults: {
    consistency?: API.RepairResult;
    missingFields?: API.RepairResult;
    originalPrice?: API.RepairResult;
    maintenance?: API.MaintenanceTaskResult;
    validation?: API.ValidationResult;
  };
}

/**
 * 结果展示组件
 */
const ResultDisplay: React.FC<ResultDisplayProps> = ({
  consistencyResult,
  orderPriceResult,
  repairResults,
}) => {
  // 数据一致性检查结果表格列
  const consistencyColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '服务ID',
      dataIndex: 'serviceId',
      key: 'serviceId',
      width: 100,
    },
    {
      title: '订单详情服务名',
      dataIndex: 'detail_service_name',
      key: 'detail_service_name',
      width: 150,
    },
    {
      title: '实际服务名',
      dataIndex: 'actual_service_name',
      key: 'actual_service_name',
      width: 150,
    },
    {
      title: '订单详情价格',
      dataIndex: 'detail_service_price',
      key: 'detail_service_price',
      width: 120,
      render: (value: number) => `¥${value.toFixed(2)}`,
    },
    {
      title: '实际价格',
      dataIndex: 'actual_service_price',
      key: 'actual_service_price',
      width: 120,
      render: (value: number) => `¥${value.toFixed(2)}`,
    },
  ];

  // 订单原价检查结果表格列
  const orderPriceColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '订单编号',
      dataIndex: 'sn',
      key: 'sn',
      width: 150,
    },
    {
      title: '原价',
      dataIndex: 'originalPrice',
      key: 'originalPrice',
      width: 100,
      render: (value: number) => (
        <Text type={value === 0 ? 'danger' : 'secondary'}>
          ¥{value.toFixed(2)}
        </Text>
      ),
    },
    {
      title: '总费用',
      dataIndex: 'totalFee',
      key: 'totalFee',
      width: 100,
      render: (value: number) => `¥${value.toFixed(2)}`,
    },
    {
      title: '权益卡抵扣',
      dataIndex: 'cardDeduction',
      key: 'cardDeduction',
      width: 120,
      render: (value: number) => `¥${value.toFixed(2)}`,
    },
    {
      title: '代金券抵扣',
      dataIndex: 'couponDeduction',
      key: 'couponDeduction',
      width: 120,
      render: (value: number) => `¥${value.toFixed(2)}`,
    },
  ];

  const hasResults =
    consistencyResult ||
    orderPriceResult ||
    Object.keys(repairResults).some(
      (key) => repairResults[key as keyof typeof repairResults],
    );

  return (
    <Row gutter={[16, 16]}>
      {/* 结果说明 */}
      {!hasResults && (
        <Col span={24}>
          <Alert
            message="操作结果展示区域"
            description={
              <div>
                <div>
                  执行检查或修复操作后，相关结果将在此处显示。您可以查看详细的数据记录、统计信息和操作结果。
                </div>
                <div style={{ marginTop: 8 }}>
                  <strong>关于异常价格：</strong>
                  异常价格订单是指原价与根据当前服务价格重新计算的价格存在较大差异的订单。
                  这种情况是正常的，可能原因包括：历史价格调整、促销活动、会员折扣、特殊业务规则等。
                  只有当异常价格率过高（&gt;5%）时才需要关注。
                </div>
              </div>
            }
            type="info"
            showIcon
            icon={<InfoCircleOutlined />}
          />
        </Col>
      )}

      {/* 数据一致性检查结果 */}
      {consistencyResult && (
        <Col span={24}>
          <Card
            title={
              <span>
                {consistencyResult?.isConsistent ? (
                  <CheckCircleOutlined
                    style={{ color: '#52c41a', marginRight: 8 }}
                  />
                ) : (
                  <ExclamationCircleOutlined
                    style={{ color: '#ff4d4f', marginRight: 8 }}
                  />
                )}
                数据一致性检查结果
              </span>
            }
          >
            <Descriptions column={2} style={{ marginBottom: 16 }}>
              <Descriptions.Item label="检查状态">
                <Tag
                  color={consistencyResult?.isConsistent ? 'success' : 'error'}
                >
                  {consistencyResult?.isConsistent ? '数据一致' : '存在不一致'}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="不一致记录数">
                <Text
                  type={
                    (consistencyResult?.inconsistentCount || 0) > 0
                      ? 'danger'
                      : 'success'
                  }
                >
                  {consistencyResult?.inconsistentCount || 0} 条
                </Text>
              </Descriptions.Item>
            </Descriptions>

            {(consistencyResult?.inconsistentRecords?.length || 0) > 0 && (
              <div>
                <Title level={5}>不一致记录详情</Title>
                <Table
                  columns={consistencyColumns}
                  dataSource={consistencyResult?.inconsistentRecords || []}
                  rowKey="id"
                  size="small"
                  scroll={{ x: 800 }}
                  pagination={{ pageSize: 10 }}
                />
              </div>
            )}
          </Card>
        </Col>
      )}

      {/* 订单原价检查结果 */}
      {orderPriceResult && (
        <Col span={24}>
          <Card
            title={
              <span>
                {!orderPriceResult?.needsRepair ? (
                  <CheckCircleOutlined
                    style={{ color: '#52c41a', marginRight: 8 }}
                  />
                ) : (
                  <ExclamationCircleOutlined
                    style={{ color: '#ff4d4f', marginRight: 8 }}
                  />
                )}
                订单原价检查结果
              </span>
            }
          >
            <Descriptions column={2} style={{ marginBottom: 16 }}>
              <Descriptions.Item label="检查状态">
                <Tag
                  color={!orderPriceResult?.needsRepair ? 'success' : 'warning'}
                >
                  {!orderPriceResult?.needsRepair ? '原价完整' : '需要修复'}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="缺失原价订单数">
                <Text
                  type={
                    (orderPriceResult?.missingCount || 0) > 0
                      ? 'warning'
                      : 'success'
                  }
                >
                  {orderPriceResult?.missingCount || 0} 个
                </Text>
              </Descriptions.Item>
            </Descriptions>

            {(orderPriceResult?.missingOrders?.length || 0) > 0 && (
              <div>
                <Title level={5}>缺失原价订单详情</Title>
                <Table
                  columns={orderPriceColumns}
                  dataSource={orderPriceResult?.missingOrders || []}
                  rowKey="id"
                  size="small"
                  scroll={{ x: 800 }}
                  pagination={{ pageSize: 10 }}
                />
              </div>
            )}
          </Card>
        </Col>
      )}

      {/* 修复结果 */}
      {Object.keys(repairResults).some(
        (key) => repairResults[key as keyof typeof repairResults],
      ) && (
        <Col span={24}>
          <Card title="修复操作结果">
            <Row gutter={[16, 16]}>
              {repairResults.consistency && (
                <Col xs={24} sm={12} lg={8}>
                  <Card size="small" title="数据一致性修复">
                    <Descriptions column={1} size="small">
                      <Descriptions.Item label="修复记录数">
                        <Text type="success">
                          {repairResults.consistency.repairedCount} 条
                        </Text>
                      </Descriptions.Item>
                      {repairResults.consistency.message && (
                        <Descriptions.Item label="结果">
                          {repairResults.consistency.message}
                        </Descriptions.Item>
                      )}
                    </Descriptions>
                  </Card>
                </Col>
              )}

              {repairResults.missingFields && (
                <Col xs={24} sm={12} lg={8}>
                  <Card size="small" title="缺失字段修复">
                    <Descriptions column={1} size="small">
                      <Descriptions.Item label="修复记录数">
                        <Text type="success">
                          {repairResults.missingFields.repairedCount} 条
                        </Text>
                      </Descriptions.Item>
                    </Descriptions>
                  </Card>
                </Col>
              )}

              {repairResults.originalPrice && (
                <Col xs={24} sm={12} lg={8}>
                  <Card size="small" title="订单原价修复">
                    <Descriptions column={1} size="small">
                      <Descriptions.Item label="修复订单数">
                        <Text type="success">
                          {repairResults.originalPrice.repairedCount} 个
                        </Text>
                      </Descriptions.Item>
                      {repairResults.originalPrice.message && (
                        <Descriptions.Item label="结果">
                          {repairResults.originalPrice.message}
                        </Descriptions.Item>
                      )}
                    </Descriptions>
                  </Card>
                </Col>
              )}

              {repairResults.validation && (
                <Col span={24}>
                  <Card
                    size="small"
                    title={
                      <span>
                        {repairResults.validation.isValid ? (
                          <CheckCircleOutlined
                            style={{ color: '#52c41a', marginRight: 8 }}
                          />
                        ) : (
                          <ExclamationCircleOutlined
                            style={{ color: '#ff4d4f', marginRight: 8 }}
                          />
                        )}
                        验证结果
                      </span>
                    }
                  >
                    <Descriptions column={3} size="small">
                      <Descriptions.Item label="验证状态">
                        <Tag
                          color={
                            repairResults.validation.isValid
                              ? 'success'
                              : 'error'
                          }
                        >
                          {repairResults.validation.isValid
                            ? '验证通过'
                            : '验证失败'}
                        </Tag>
                      </Descriptions.Item>
                      <Descriptions.Item label="不一致记录数">
                        {repairResults.validation.inconsistentCount} 条
                      </Descriptions.Item>
                      <Descriptions.Item label="验证信息">
                        {repairResults.validation.message}
                      </Descriptions.Item>
                    </Descriptions>
                  </Card>
                </Col>
              )}
            </Row>
          </Card>
        </Col>
      )}
    </Row>
  );
};

export default ResultDisplay;
