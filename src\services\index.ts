// 此文件由插件自动生成，请勿手动修改
// @ts-nocheck
// prettier-ignore
// eslint-disable
import * as additionalService from './additional-service';
import * as area from './area';
import * as auth from './auth';
import * as banner from './banner';
import * as complaints from './complaints';
import * as coupons from './coupons';
import * as customerCoupons from './customer-coupons';
import * as customerMembershipCards from './customer-membership-cards';
import * as customer from './customer';
import * as customers from './customers';
import * as dataConsistency from './data-consistency';
import * as dictionaries from './dictionaries';
import * as employees from './employees';
import * as features from './features';
import * as membershipCardOrders from './membership-card-orders';
import * as membershipCardTypes from './membership-card-types';
import * as order from './order';
import * as permissions from './permissions';
import * as promotionRecord from './promotion-record';
import * as reviews from './reviews';
import * as rightsCard from './rights-card';
import * as roles from './roles';
import * as servicePhotos from './service-photos';
import * as serviceType from './service-type';
import * as service from './service';
import * as users from './users';
import * as vehicles from './vehicles';

export {
  additionalService,
  area,
  auth,
  banner,
  complaints,
  coupons,
  customerCoupons,
  customerMembershipCards,
  customer,
  customers,
  dataConsistency,
  dictionaries,
  employees,
  features,
  membershipCardOrders,
  membershipCardTypes,
  order,
  permissions,
  promotionRecord,
  reviews,
  rightsCard,
  roles,
  servicePhotos,
  serviceType,
  service,
  users,
  vehicles
};
